<template>
  <div class="reset-password-container">
    <form class="reset-password-card" @submit.prevent="sendResetEmail">
      <div class="form-header">
        <h3>Reset your password</h3>
        <p class="muted">
          Enter your email and we'll send you a link to reset your password.
        </p>
      </div>
      <div class="form-group">
        <label for="email" class="label">Email</label>
        <input
          type="email"
          id="email"
          class="input"
          v-model="email"
          placeholder="<EMAIL>"
        />
      </div>
      <button type="submit">Send reset email</button>
    </form>
  </div>
</template>
<script>
import { ref } from "vue";
export default {
  name: "ForgotPassword",
  setup() {
    const email = ref("");

    const sendResetEmail = () => {
      console.log("Send reset email!", email.value);
    };

    return {
      email,
      sendResetEmail,
    };
  },
};
</script>
<style scoped>
@import "@/components/auth-contents/styles/auth.module.css";

.reset-password-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f0f0f0;
}

.reset-password-card {
  background-color: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-header p {
  margin-top: 0.7rem;
}
</style>

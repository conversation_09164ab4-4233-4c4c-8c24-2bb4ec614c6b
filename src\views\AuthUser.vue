<template>
  <div class="auth-container">
    <!-- Auth Tab buttons -->
    <AuthTabs :activeTab="activeTab" @update-tab="updateActiveTab" />
    <!-- Google Auth Button component -->

    <GoogleAuthButton
      :buttonText="
        activeTab === 'signin' ? 'Sign in with Google' : 'Sign up with Google'
      "
      @google-auth="handleGoogleAuth"
    />
    <!-- Google Auth Button component end -->
    <div class="auth-content">
      <transition name="slide" mode="out-in">
        <!-- Use SignInForm or SignUpForm based on the active tab -->
        <SignInForm
          v-if="activeTab === 'signin'"
          @submit-signin="handleSignIn"
        />
        <SignUpForm v-else @submit-signup="handleSignUp" />
      </transition>
    </div>
  </div>
</template>

<script>
import { ref } from "vue";
import AuthTabs from "@/components/userAuth/AuthTabs.vue";
import GoogleAuthButton from "@/components/userAuth/GoogleAuthButton.vue";
import SignInForm from "@/components/auth-contents/SignInForm.vue";
import SignUpForm from "@/components/auth-contents/SignUpForm.vue";
import { useRouter } from "vue-router";
import { signInWithPopup } from "firebase/auth";
import { auth, googleProvider } from "../firebase/config.js";

export default {
  name: "AuthUser",
  components: {
    AuthTabs,
    GoogleAuthButton,
    SignInForm,
    SignUpForm,
  },
  setup() {
    const activeTab = ref("signin");
    const router = useRouter();

    const signin = ref({
      email: "",
      password: "",
    });

    const signup = ref({
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
    });

    const handleSignIn = () => {
      // Implement sign in logic here
      console.log("Sign In:", signin.value);
    };

    const handleSignUp = () => {
      // Implement sign up logic here
      console.log("Sign Up:", signup.value);
    };

    const handleGoogleAuth = async () => {
      try {
        await signInWithPopup(auth, googleProvider);
        router.push("/");
      } catch (error) {
        console.error("Error logging in with Google:", error);
      }
    };

    const updateActiveTab = (tab) => {
      activeTab.value = tab;
    };
    return {
      activeTab,
      signin,
      signup,
      handleSignIn,
      handleSignUp,
      updateActiveTab,
      handleGoogleAuth,
    };
  },
};
</script>

<style scoped>
.auth-container {
  max-width: 400px;
  margin: 20px auto;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from {
  transform: translateX(5%);
  opacity: 0;
}

.slide-leave-to {
  transform: translateX(-5%);
  opacity: 0;
}

.slide-enter-to,
.slide-leave-from {
  transform: translateX(0);
  opacity: 1;
}
</style>

import { ref } from "vue";
import {
  getAuth,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
} from "firebase/auth";

export function useAuth() {
  const isAuthenticated = ref(false);
  const user = ref(null);

  const auth = getAuth();
  const provider = new GoogleAuthProvider();

  const login = async () => {
    try {
      const result = await signInWithPopup(auth, provider);
      user.value = result.user;
      isAuthenticated.value = true;
    } catch (error) {
      console.error("Error during authentication:", error);
    }
  };

  const logout = async () => {
    try {
      await signOut(auth);
      user.value = null;
      isAuthenticated.value = false;
    } catch (error) {
      console.error("Error during logout:", error);
    }
  };

  return {
    isAuthenticated,
    user,
    login,
    logout,
  };
}

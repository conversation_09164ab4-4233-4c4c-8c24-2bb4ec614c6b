import { initializeApp } from "firebase/app";
import { getFirestore, serverTimestamp } from "firebase/firestore";
import { getAuth, GoogleAuthProvider } from "firebase/auth";

const firebaseConfig = {
  apiKey: "AIzaSyClSM6aN-xXm6Ns04mPfowzUtMbnL2P_gA",
  authDomain: "recipe-1f726.firebaseapp.com",
  projectId: "recipe-1f726",
  storageBucket: "recipe-1f726.appspot.com",
  messagingSenderId: "665056535178",
  appId: "1:665056535178:web:e575344447b5a4bfe4c373",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firestore
const db = getFirestore(app);

// Initialize Auth
const auth = getAuth(app);
const googleProvider = new GoogleAuthProvider();

// Initialize timestamp
const timestamp = serverTimestamp;

export { db, auth, googleProvider, timestamp };

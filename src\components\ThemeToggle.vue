<template>
  <button
    class="theme-toggle"
    @click="toggleTheme"
    :aria-label="isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'"
    :title="isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'"
  >
    <transition name="icon-fade" mode="out-in">
      <Sun v-if="!isDarkMode" key="sun" class="theme-icon" />
      <Moon v-else key="moon" class="theme-icon" />
    </transition>
  </button>
</template>

<script>
import { useTheme } from "@/composables/useTheme";
import Sun from "@/components/icons/Sun.vue";
import Moon from "@/components/icons/Moon.vue";

export default {
  name: "ThemeToggle",
  components: {
    Sun,
    Moon,
  },
  setup() {
    const { isDarkMode, toggleTheme } = useTheme();

    return {
      isDarkMode,
      toggleTheme,
    };
  },
};
</script>

<style scoped>
.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 25px;
  height: 25px;
  border: none;
  border-radius: 50%;
  background-color: transparent;
  cursor: pointer;
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
  padding: 6px;
}

.theme-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(1.05);
}

.theme-toggle:active {
  transform: scale(0.95);
}

.theme-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-color);
}

.theme-icon {
  width: 24px;
  height: 24px;
  color: var(--primary-color);
  transition: all var(--transition-medium);
}

/* Icon transition animations */
.icon-fade-enter-active,
.icon-fade-leave-active {
  transition: all 0.3s ease;
}

.icon-fade-enter-from {
  opacity: 0;
  transform: rotate(180deg) scale(0.5);
}

.icon-fade-leave-to {
  opacity: 0;
  transform: rotate(-180deg) scale(0.5);
}

.icon-fade-enter-to,
.icon-fade-leave-from {
  opacity: 1;
  transform: rotate(0deg) scale(1);
}

/* Dark theme styles */
:global([data-theme="dark"]) .theme-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

:global([data-theme="dark"]) .theme-icon {
  color: var(--primary-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .theme-toggle {
    width: 36px;
    height: 36px;
  }

  .theme-icon {
    width: 18px;
    height: 18px;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .theme-toggle,
  .theme-icon,
  .icon-fade-enter-active,
  .icon-fade-leave-active {
    transition: none;
  }

  .icon-fade-enter-from,
  .icon-fade-leave-to {
    transform: scale(0.5);
  }
}
</style>

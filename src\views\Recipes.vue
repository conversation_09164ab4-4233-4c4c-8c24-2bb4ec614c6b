<!-- Here the recipes will be displayed as a route -->

<template>
  <div class="recipes-page">
    <Container>
      <header class="section-heading">
        <div class="heading-text">
          <h2 class="section-title">Breakfast Recipes</h2>
          <p class="muted">
            Discover delicious breakfast recipes to start your day right
          </p>
        </div>
      </header>
      <div v-if="isLoading" class="loading-container"><Loading /></div>
      <Error v-else-if="error" :errorMessage="error" />
      <RecipeList v-else :recipes="recipes" />
    </Container>
  </div>
</template>

<script>
import { onMounted } from "vue";
import useFetchRecipes from "@/composables/useFetchRecipes";
import Container from "@/components/Container.vue";

import RecipeList from "@/components/RecipeList.vue";
import Loading from "@/components/Loading.vue";
import Error from "@/components/Error.vue";

export default {
  name: "Recipes",
  components: {
    Container,
    RecipeList,
    Loading,
    Error,
  },
  setup() {
    const { recipes, error, isLoading, fetchRecipes } = useFetchRecipes();

    onMounted(() => fetchRecipes("breakfast"));

    return { recipes, error, isLoading };
  },
};
</script>

<style scoped>
.recipes-page {
  background-color: var(--bg-color-light);
  min-height: 100vh;
  padding: 40px 0;
  transition: background-color var(--transition-medium);
}

.section-heading {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-light);
}

.section-title {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--text-color);
  font-weight: 600;
  transition: color var(--transition-medium);
}

h2.section-title {
  margin-bottom: 0.2rem;
}

.muted {
  color: var(--text-muted);
  font-size: 1rem;
  transition: color var(--transition-medium);
}

.see-all-link {
  text-decoration: none;
  color: var(--primary-color);
  font-weight: 600;
  padding: 8px 16px;
  border-radius: var(--border-radius);
  transition: all var(--transition-medium);
  border: 1px solid transparent;
}

.see-all-link:hover {
  color: var(--primary-color-light);
  background-color: var(--card-bg);
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* Responsive design */
@media (max-width: 768px) {
  .recipes-page {
    padding: 20px 0;
  }

  .section-heading {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .see-all-link {
    align-self: flex-end;
  }
}
</style>

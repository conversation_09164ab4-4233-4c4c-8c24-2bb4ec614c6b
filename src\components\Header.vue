<template>
  <Container>
    <header class="header">
      <div class="hero">
        <div class="hero-content">
          <h1>The Easiest Way to Make Your Favorite Meals</h1>
          <p>
            Discover a world of 1000+ recipes, from classic comfort foods to
            exotic dishes. Help you to find and make your favorite meals.
          </p>
          <button class="btn btn-primary" @click="navigateToRecipe">
            Explore Recipes
          </button>
        </div>
        <div class="hero-image">
          <img class="hero-dish" :src="dish" alt="Dish Image" />
        </div>
      </div>
    </header>
  </Container>
</template>

<script>
import Logo from "./icons/Logo.vue";
import Container from "./Container.vue";
import Icon from "./Icon.vue";
import dish from "../assets/images/dishe.png";
import { ref } from "vue";
import { useRouter } from "vue-router";

export default {
  name: "Header",
  components: {
    Container,
    Icon,
    Logo,
  },

  setup() {
    const router = useRouter();
    const dishRef = ref(dish);
    const navigateToRecipe = () => {
      router.push({ name: "Recipes" });
    };

    return {
      dish: dishRef,
      navigateToRecipe,
    };
  },
};
</script>

<style scoped>
.header {
  background-color: var(--bg-color-light);
  transition: background-color var(--transition-medium);
}

.hero {
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 20px;
  width: 50%;
}

.hero-content h1 {
  font-size: 45px;
  font-weight: 700;
  letter-spacing: 1px;
  color: var(--text-color);
  width: 65%;
  text-transform: capitalize;
  transition: color var(--transition-medium);
}

.hero-content p {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  color: var(--text-color);
  width: 65%;
  transition: color var(--transition-medium);
}

.btn {
  display: inline-block;
  padding: 10px 20px;
  font-size: 15px;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  border: none;
  border-radius: var(--border-radius);
  transition: all var(--transition-medium);
}

.btn-primary {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
}

.btn-primary:hover {
  background-color: var(--button-primary-hover);
  box-shadow: 0 0 0 3px var(--card-shadow);
  transform: translateY(-2px);
}

.btn-primary:active {
  transform: translateY(1px);
}

.hero-image {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-dish {
  width: 550px;
  object-fit: cover;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  transition: transform var(--transition-medium);
}

.hero-dish:hover {
  transform: scale(1.02);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero {
    flex-direction: column;
    height: auto;
    padding: 40px 0;
    text-align: center;
  }

  .hero-content {
    width: 100%;
    align-items: center;
  }

  .hero-content h1,
  .hero-content p {
    width: 100%;
  }

  .hero-content h1 {
    font-size: 32px;
  }

  .hero-image {
    width: 100%;
    margin-top: 20px;
  }

  .hero-dish {
    width: 100%;
    max-width: 400px;
  }
}

@media (max-width: 480px) {
  .hero-content h1 {
    font-size: 28px;
  }

  .hero-content p {
    font-size: 14px;
  }

  .btn {
    padding: 12px 24px;
    font-size: 16px;
  }
}
</style>

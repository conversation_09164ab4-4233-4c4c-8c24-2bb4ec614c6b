import { createRouter, createWebHistory } from "vue-router";
import HomeView from "../views/HomeView.vue";
import AuthView from "../views/AuthUser.vue";
import ForgotPassword from "@/views/ForgotPassword.vue";
import Recipes from "@/views/Recipes.vue";
import RecipeDetail from "@/views/RecipeDetail.vue";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      name: "home",
      component: HomeView,
    },
    {
      path: "/recipes",
      name: "Recipes",
      component: Recipes,
    },
    {
      path: "/add-recipe",
      name: "AddRecipe",
      // component: AddRecipeView,
    },
    {
      path: "/articles",
      name: "articles",
      // component: ArticlesView,
    },
    {
      path: "/auth",
      name: "Auth",
      component: AuthView,
    },
    {
      name: "reset-password",
      path: "/ForgotPassword",
      component: ForgotPassword,
    },
    {
      path: "/recipe/:id",
      name: "RecipeDetail",
      component: RecipeDetail,
    },
  ],
});

export default router;

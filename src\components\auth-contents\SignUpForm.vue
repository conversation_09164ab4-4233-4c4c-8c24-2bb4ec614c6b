<!-- SignUpForm.vue -->
<template>
  <form @submit.prevent="handleSubmit">
    <div class="form-header">
      <h3>Get Started</h3>
      <p>Create a new account</p>
    </div>

    <div class="form-group">
      <label for="signup-name">Name</label>
      <input
        type="text"
        id="signup-name"
        v-model="name"
        placeholder="Name"
        @blur="validateName"
        :class="{ invalid: nameError }"
        required
      />
      <span v-if="nameError" class="error">{{ nameError }}</span>
    </div>

    <div class="form-group">
      <label for="signup-email">Email</label>
      <input
        type="email"
        id="signup-email"
        v-model="email"
        placeholder="<EMAIL>"
        @blur="validateEmail"
        :class="{ invalid: emailError }"
        required
      />
      <span v-if="emailError" class="error">{{ emailError }}</span>
    </div>

    <div class="form-group">
      <label for="signup-password">Password</label>
      <input
        type="password"
        id="signup-password"
        v-model="password"
        placeholder="Password"
        @blur="validatePassword"
        :class="{ invalid: passwordError }"
        required
      />
      <span v-if="passwordError" class="error">{{ passwordError }}</span>
    </div>

    <div class="form-group">
      <label for="signup-confirm-password">Confirm Password</label>
      <input
        type="password"
        id="signup-confirm-password"
        v-model="confirmPassword"
        placeholder="Confirm Password"
        @blur="validateConfirmPassword"
        :class="{ invalid: confirmPasswordError }"
        required
      />
      <span v-if="confirmPasswordError" class="error">{{
        confirmPasswordError
      }}</span>
    </div>

    <button type="submit">Sign Up</button>
  </form>
</template>

<script>
import { ref } from "vue";

export default {
  name: "SignUpForm",
  emits: ["submit-signup"],
  setup(_, { emit }) {
    const name = ref("");
    const email = ref("");
    const password = ref("");
    const confirmPassword = ref("");

    const nameError = ref(null);
    const emailError = ref(null);
    const passwordError = ref(null);
    const confirmPasswordError = ref(null);

    const validateName = () => {
      nameError.value = name.value.trim() === "" ? "Name is required" : null;
    };

    const validateEmail = () => {
      emailError.value = !email.value.includes("@")
        ? "Invalid email address"
        : null;
    };

    const validatePassword = () => {
      passwordError.value =
        password.value.length < 6
          ? "Password must be at least 6 characters"
          : null;
    };

    const validateConfirmPassword = () => {
      confirmPasswordError.value =
        password.value !== confirmPassword.value
          ? "Passwords do not match"
          : null;
    };

    const handleSubmit = () => {
      validateName();
      validateEmail();
      validatePassword();
      validateConfirmPassword();

      if (
        !nameError.value &&
        !emailError.value &&
        !passwordError.value &&
        !confirmPasswordError.value
      ) {
        emit("submit-signup", {
          name: name.value,
          email: email.value,
          password: password.value,
        });
      }
    };

    return {
      name,
      email,
      password,
      confirmPassword,
      nameError,
      emailError,
      passwordError,
      confirmPasswordError,
      validateName,
      validateEmail,
      validatePassword,
      validateConfirmPassword,
      handleSubmit,
    };
  },
};
</script>

<style scoped>
@import "./styles/auth.module.css";

.invalid {
  border-color: red;
}

.error {
  color: red;
  font-size: 0.8rem;
}
</style>

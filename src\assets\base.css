:root {
  --primary-color: #e45700;
  --primary-color-light: #bc6128;
  --secondary-color: #f5f5f5;
  --bg-color-light: #f1dfd4c5;
  --font-family: "Roboto", sans-serif;
  --base-font-size: 16px;
  --text-color: #2f2f2f;
  --text-muted: #b7b7b7;
  --bg-color: #ffffff;
  --nav-bg: #ffffff;
  --nav-text: #2f2f2f;
  --footer-bg: #1e4f4f;
  --footer-text: #d4d7dd;

  /* Card and Component Colors */
  --card-bg: #ffffff;
  --card-border: #e0e0e0;
  --card-shadow: rgba(0, 0, 0, 0.1);

  /* Button Colors */
  --button-primary-bg: #e45700;
  --button-primary-text: #ffffff;
  --button-primary-hover: #bc6128;
  --button-secondary-bg: #f5f5f5;
  --button-secondary-text: #2f2f2f;
  --button-secondary-hover: #e0e0e0;

  /* Form Colors */
  --input-bg: #ffffff;
  --input-border: #ddd;
  --input-border-focus: #e45700;
  --input-text: #2f2f2f;
  --input-placeholder: #b7b7b7;

  /* Alert Colors */
  --alert-info-bg: #d1ecf1;
  --alert-info-border: #67c2ef;
  --alert-info-text: #005693;
  --alert-success-bg: #d4edda;
  --alert-success-border: #c3e6cb;
  --alert-success-text: #155724;
  --alert-warning-bg: #fff3cd;
  --alert-warning-border: #ffc107;
  --alert-warning-text: #856404;
  --alert-error-bg: #f8d7da;
  --alert-error-border: #f5c6cb;
  --alert-error-text: #721c24;

  /* Modal Colors */
  --modal-bg: #ffffff;
  --modal-overlay: rgba(0, 0, 0, 0.5);
  --modal-text: #2f2f2f;

  /* Border Colors */
  --border-light: #e0e0e0;
  --border-medium: #ccc;
  --border-dark: #999;

  /* Layout Variables */
  --header-height: 80px;
  --footer-height: auto;
  --container-max-width: 1200px;
  --container-padding: 20px;
  --section-spacing: 40px;
  --border-radius: 8px;

  /* Shadow Variables */
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.15);

  /* Transition Variables */
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.4s ease;
}

:root[data-theme="dark"] {
  --primary-color: #ff6b1a;
  --primary-color-light: #ff8c4d;
  --secondary-color: #2d2d2d;
  --bg-color: #1a1a1a;
  --bg-color-light: #1a1a1a;
  --text-color: #e0e0e0;
  --text-muted: #888888;
  --nav-bg: #2d2d2d;
  --nav-text: #e0e0e0;
  --footer-bg: #0f2f2f;
  --footer-text: #b4b7bd;

  /* Card and Component Colors */
  --card-bg: #2d2d2d;
  --card-border: #404040;
  --card-shadow: rgba(0, 0, 0, 0.3);

  /* Button Colors */
  --button-primary-bg: #ff6b1a;
  --button-primary-text: #ffffff;
  --button-primary-hover: #ff8c4d;
  --button-secondary-bg: #404040;
  --button-secondary-text: #e0e0e0;
  --button-secondary-hover: #505050;

  /* Form Colors */
  --input-bg: #2d2d2d;
  --input-border: #404040;
  --input-border-focus: #ff6b1a;
  --input-text: #e0e0e0;
  --input-placeholder: #888888;

  /* Alert Colors */
  --alert-info-bg: #1e3a5f;
  --alert-info-border: #2980b9;
  --alert-info-text: #85c1e9;
  --alert-success-bg: #1e5f3a;
  --alert-success-border: #27ae60;
  --alert-success-text: #82e0aa;
  --alert-warning-bg: #5f4e1e;
  --alert-warning-border: #f39c12;
  --alert-warning-text: #f7dc6f;
  --alert-error-bg: #5f1e1e;
  --alert-error-border: #e74c3c;
  --alert-error-text: #f1948a;

  /* Modal Colors */
  --modal-bg: #2d2d2d;
  --modal-overlay: rgba(0, 0, 0, 0.7);
  --modal-text: #e0e0e0;

  /* Border Colors */
  --border-light: #404040;
  --border-medium: #505050;
  --border-dark: #606060;

  /* Dark theme specific shadows */
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.4);
  --shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.5);
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-weight: 400;
}

img {
  max-width: 100%;
  height: auto;
}

a {
  text-decoration: none;
  transition: 0.4s;
  display: inline-block;
}

ul {
  list-style: none;
}

html {
  box-sizing: border-box;
  scroll-behavior: smooth;
}

body {
  color: var(--text-color);
  background: var(--bg-color-light);
  font-family: var(--font-family);
  font-size: var(--base-font-size);
  line-height: 1.6;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  transition: background-color var(--transition-medium),
    color var(--transition-medium);
}

/* Layout Utilities */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
  width: 100%;
}

.section {
  margin: var(--section-spacing) 0;
}

/* Theme-aware Utility Classes */
.text-primary {
  color: var(--primary-color);
  transition: color var(--transition-medium);
}

.text-secondary {
  color: var(--text-muted);
  transition: color var(--transition-medium);
}

.bg-card {
  background-color: var(--card-bg);
  transition: background-color var(--transition-medium);
}

.border-theme {
  border-color: var(--border-light);
  transition: border-color var(--transition-medium);
}

.shadow-theme {
  box-shadow: var(--shadow-light);
}

.btn-theme {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  border: none;
  padding: 10px 20px;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 500;
  transition: all var(--transition-medium);
}

.btn-theme:hover {
  background-color: var(--button-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  border: 1px solid var(--border-light);
}

.btn-secondary:hover {
  background-color: var(--button-secondary-hover);
}

/* Muted text utility */
.muted {
  color: var(--text-muted) !important;
  transition: color var(--transition-medium);
}

/* Responsive Utilities */
@media (max-width: 768px) {
  :root {
    --container-padding: 16px;
    --section-spacing: 24px;
  }
}

@media (max-width: 480px) {
  :root {
    --container-padding: 12px;
    --section-spacing: 20px;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

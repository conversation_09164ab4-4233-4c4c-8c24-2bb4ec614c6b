import { ref, watch, onMounted } from "vue";

// Theme state
const isDarkMode = ref(false);

// Theme management composable
export function useTheme() {
  // Initialize theme from localStorage or system preference
  const initializeTheme = () => {
    const savedTheme = localStorage.getItem("theme");
    const prefersDark = window.matchMedia(
      "(prefers-color-scheme: dark)"
    ).matches;

    if (savedTheme) {
      isDarkMode.value = savedTheme === "dark";
    } else {
      isDarkMode.value = prefersDark;
    }

    applyTheme();
  };

  // Apply theme to document
  const applyTheme = () => {
    const root = document.documentElement;

    if (isDarkMode.value) {
      root.setAttribute("data-theme", "dark");
    } else {
      root.removeAttribute("data-theme");
    }
  };

  // Toggle theme
  const toggleTheme = () => {
    isDarkMode.value = !isDarkMode.value;
    localStorage.setItem("theme", isDarkMode.value ? "dark" : "light");
    applyTheme();
  };

  // Set specific theme
  const setTheme = (theme) => {
    isDarkMode.value = theme === "dark";
    localStorage.setItem("theme", theme);
    applyTheme();
  };

  // Watch for system theme changes
  const watchSystemTheme = () => {
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    mediaQuery.addEventListener("change", (e) => {
      // Only update if user hasn't manually set a preference
      if (!localStorage.getItem("theme")) {
        isDarkMode.value = e.matches;
        applyTheme();
      }
    });
  };

  // Watch for theme changes
  watch(isDarkMode, applyTheme);

  return {
    isDarkMode,
    toggleTheme,
    setTheme,
    initializeTheme,
    watchSystemTheme,
  };
}

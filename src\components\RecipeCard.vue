<template>
  <div class="recipe-card">
    <RouterLink :to="{ name: 'RecipeDetail', params: { id: recipe.id } }">
      <div class="recipe-image-container">
        <img :src="recipe.image" :alt="recipe.title" loading="lazy" />
      </div>
      <div class="recipe-card-body">
        <h3 class="recipe-card-title">{{ recipe.title }}</h3>
        <p class="muted recipe-card-description">{{ recipe.description }}</p>
      </div>
      <div class="recipe-card-footer"></div>
    </RouterLink>
  </div>
</template>

<script>
export default {
  name: "RecipeCard",
  props: {
    recipe: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style scoped>
.recipe-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.recipe-card {
  width: 300px;
  border-radius: var(--border-radius);
  overflow: hidden;
  background-color: var(--card-bg);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--card-border);
  transition: all var(--transition-medium);
}

.recipe-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.recipe-card a {
  text-decoration: none;
  color: var(--text-color);
  transition: color var(--transition-medium);
}

.recipe-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.recipe-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform var(--transition-medium);
}

.recipe-image-container::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity var(--transition-medium);
}

.recipe-card:hover .recipe-image-container::after {
  opacity: 1;
}

.recipe-card:hover .recipe-image-container img {
  transform: scale(1.05);
}

.recipe-card-body {
  padding: 16px;
  background-color: var(--card-bg);
  transition: background-color var(--transition-medium);
}

.recipe-card-title {
  font-size: 1.25rem;
  margin-bottom: 8px;
  color: var(--text-color);
  font-weight: 600;
  transition: color var(--transition-medium);
}

.recipe-card-description {
  font-size: 0.9rem;
  color: var(--text-muted);
  line-height: 1.4;
  transition: color var(--transition-medium);
}

.recipe-card-footer {
  padding: 12px 16px;
  border-top: 1px solid var(--border-light);
  background-color: var(--card-bg);
  transition: all var(--transition-medium);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .recipe-card {
    width: 100%;
    max-width: 350px;
    margin: 0 auto;
  }

  .recipe-cards {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .recipe-card-body {
    padding: 12px;
  }

  .recipe-card-title {
    font-size: 1.1rem;
  }

  .recipe-card-description {
    font-size: 0.85rem;
  }

  .recipe-card-footer {
    padding: 10px 12px;
  }
}
</style>

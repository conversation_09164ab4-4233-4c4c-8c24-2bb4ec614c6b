<!-- AuthTab.vue -->
<template>
  <div class="auth-tabs">
    <button
      @click="$emit('update-tab', 'signin')"
      :class="{ active: activeTab === 'signin' }"
    >
      Sign In
    </button>
    <button
      @click="$emit('update-tab', 'signup')"
      :class="{ active: activeTab === 'signup' }"
    >
      Sign Up
    </button>
  </div>
</template>

<script>
export default {
  name: "AuthTab",
  props: {
    activeTab: {
      type: String,
      required: true,
    },
  },
};
</script>

<style scoped>
.auth-tabs {
  display: flex;
  margin-bottom: 20px;
}

.auth-tabs button {
  flex: 1;
  width: 48%;
  margin: 0 1%;
  padding: 10px;
  background-color: #e0e0e0;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
  border-radius: 5px;
}

.auth-tabs button.active {
  background-color: #007bff;
  color: white;
}
</style>

<template>
  <div class="recipe-detail">
    <div class="recipe-image-container">
      <img :src="recipe.image" :alt="recipe.title" loading="lazy" />
    </div>
    <div class="recipe-detail-body">
      <h1 class="recipe-detail-title">{{ recipe.title }}</h1>
      <p class="recipe-detail-description">{{ recipe.description }}</p>
      <div class="recipe-detail-info">
        <h2>Ingredients</h2>
        <ul>
          <li v-for="ingredient in recipe.ingredients" :key="ingredient">
            {{ ingredient }}
          </li>
        </ul>
        <h2>Instructions</h2>
        <ol>
          <li v-for="(step, index) in recipe.instructions" :key="index">
            {{ step }}
          </li>
        </ol>
      </div>
    </div>
    <div class="recipe-detail-footer">
      <!-- Add any additional information or actions here -->
    </div>
  </div>
</template>

<script>
export default {
  name: "RecipeDetail",
  data() {
    return {
      recipe: {
        id: 1,
        title: "Sample Recipe",
        description: "This is a sample recipe description.",
        image: "https://example.com/sample-recipe-image.jpg",
        ingredients: ["Ingredient 1", "Ingredient 2", "Ingredient 3"],
        instructions: [
          "Step 1: Do this",
          "Step 2: Do that",
          "Step 3: Finish up",
        ],
      },
    };
  },
};
</script>

<style scoped>
.recipe-detail {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.recipe-image-container {
  position: relative;
  height: 400px;
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.recipe-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recipe-detail-body {
  padding: 1rem 0;
}

.recipe-detail-title {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.recipe-detail-description {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 2rem;
}

.recipe-detail-info h2 {
  font-size: 1.5rem;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.recipe-detail-info ul,
.recipe-detail-info ol {
  padding-left: 1.5rem;
}

.recipe-detail-info li {
  margin-bottom: 0.5rem;
}

.recipe-detail-footer {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}
</style>

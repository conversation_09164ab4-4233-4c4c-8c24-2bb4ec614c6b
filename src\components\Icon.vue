<template>
  <span
    class="icon"
    v-html="svg"
    :style="{ width: size + 'px', height: size + 'px' }"
  ></span>
</template>

<script>
export default {
  name: "Icon",
  props: {
    svg: {
      type: String,
      required: true,
    },
    size: {
      type: Number,
      default: 24,
    },
  },
};
</script>

<style scoped>
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.icon svg {
  width: 10%;
  height: 100%;
}
</style>

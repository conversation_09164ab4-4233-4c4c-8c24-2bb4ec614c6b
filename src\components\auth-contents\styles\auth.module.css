form {
  display: flex;
  flex-direction: column;
  font: inherit;
  background-color: var(--card-bg);
  padding: 24px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--card-border);
  transition: all var(--transition-medium);
}

.form-header {
  margin: 10px 0;
}

.form-header h3 {
  font-weight: bold;
  font-size: 1.2rem;
  color: var(--text-color);
  transition: color var(--transition-medium);
}

.form-header p {
  color: var(--text-muted);
  font-size: 0.8rem;
  transition: color var(--transition-medium);
}

.form-group {
  margin-bottom: 15px;
}

label {
  display: block;
  margin: 0 0 5px 2px;
  font-size: 12px;
  color: var(--text-color);
  font-weight: 500;
  transition: color var(--transition-medium);
}

input {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--input-border);
  border-radius: var(--border-radius);
  background-color: var(--input-bg);
  color: var(--input-text);
  font-size: 14px;
  transition: all var(--transition-medium);
}

input:focus {
  outline: none;
  border-color: var(--input-border-focus);
  box-shadow: 0 0 0 3px rgba(228, 87, 0, 0.1);
}

input::placeholder {
  color: var(--input-placeholder);
}

button[type="submit"] {
  padding: 12px;
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: all var(--transition-medium);
}

button[type="submit"]:hover {
  background-color: var(--button-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

button[type="submit"]:active {
  transform: translateY(0);
}

button[type="submit"]:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Responsive design */
@media (max-width: 768px) {
  form {
    padding: 20px;
  }

  input,
  button[type="submit"] {
    padding: 10px;
  }
}

<!-- GoogleAuthButton.vue -->
<template>
  <button @click="handleGoogleAuth" class="google-auth-btn">
    <img src="@/assets/images/google.png" alt="Google Icon" />
    {{ buttonText }}
  </button>
</template>

<script>
export default {
  name: "GoogleAuthButton",
  props: {
    buttonText: {
      type: String,
      required: true,
    },
  },
  emits: ["google-auth"],
  setup() {
    const handleGoogleAuth = () => {
      emit("google-auth");
    };
    return { handleGoogleAuth };
  },
};
</script>

<style scoped>
.google-auth-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 10px;
  margin-bottom: 20px;
  background-color: #fff;
  color: #757575;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.google-auth-btn:hover {
  background-color: #f1f1f1;
}

.google-auth-btn img {
  width: 18px;
  height: 18px;
  margin-right: 10px;
}
</style>

import { ref } from "vue";

const apiKey = import.meta.env.VITE_RECIPE_API_KEY;

const useFetchRecipes = () => {
  const recipes = ref([]);
  const error = ref(null);
  const isLoading = ref(false);

  const fetchRecipes = async (dishType = "salad") => {
    const numRecipes = Math.floor(Math.random() * 20) + 10;

    isLoading.value = true;
    error.value = null;

    try {
      const response = await fetch(
        `https://api.spoonacular.com/recipes/complexSearch?type=${dishType}&number=${numRecipes}&apiKey=${apiKey}`
      );

      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);

      const data = await response.json();
      recipes.value = data.results;
    } catch (err) {
      error.value = err.message;
    } finally {
      isLoading.value = false;
    }
  };

  return {
    recipes,
    error,
    isLoading,
    fetchRecipes,
  };
};

export default useFetchRecipes;
